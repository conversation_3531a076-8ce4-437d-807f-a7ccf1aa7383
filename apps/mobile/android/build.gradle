buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.24"
    }
    repositories {
        google()
        mavenCentral()
        maven { url("$rootDir/../node_modules/react-native/android") }
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.7.0")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
    }
}

allprojects {
    repositories {
        mavenCentral()
        google()
        maven { url("$rootDir/../node_modules/react-native/android") }
        maven {
            url 'https://api.mapbox.com/downloads/v2/releases/maven'
            authentication { basic(BasicAuthentication) }
            credentials {
                username = 'mapbox'
                password = System.getenv('MAPBOX_DOWNLOADS_TOKEN') ?: project.properties['MAPBOX_DOWNLOADS_TOKEN'] ?: ""
            }
        }
    }
    
    // Global configuration for all projects
    afterEvaluate { project ->
        if (project.hasProperty('android')) {
            project.android {
                compileSdkVersion rootProject.ext.compileSdkVersion
                buildToolsVersion rootProject.ext.buildToolsVersion
                
                // BuildConfig özelliğini etkinleştir
                buildFeatures {
                    buildConfig true
                }
                
                // CompileOptions ayarla
                compileOptions {
                    sourceCompatibility JavaVersion.VERSION_17
                    targetCompatibility JavaVersion.VERSION_17
                }
                
                // DefaultConfig ayarla
                if (project.android.hasProperty('defaultConfig')) {
                    project.android.defaultConfig {
                        minSdkVersion rootProject.ext.minSdkVersion
                        targetSdkVersion rootProject.ext.targetSdkVersion
                    }
                }
                
                // Namespace ayarla
                if (!project.android.hasProperty('namespace') || project.android.namespace == null) {
                    def manifestFile = project.file('src/main/AndroidManifest.xml')
                    if (manifestFile.exists()) {
                        def manifestText = manifestFile.text
                        def packageMatch = manifestText =~ /package\s*=\s*"([^"]+)"/
                        if (packageMatch) {
                            project.android.namespace = packageMatch[0][1]
                        }
                    }
                    
                    // React Native paketleri için özel namespace'ler
                    switch (project.name) {
                        case 'react-native-linear-gradient':
                            project.android.namespace = 'com.BV.LinearGradient'
                            break
                        case 'react-native-gesture-handler':
                            project.android.namespace = 'com.swmansion.gesturehandler'
                            break
                        case 'react-native-reanimated':
                            project.android.namespace = 'com.swmansion.reanimated'
                            break
                        case 'react-native-safe-area-context':
                            project.android.namespace = 'com.th3rdwave.safeareacontext'
                            break
                        case { it.contains('react-native-async-storage') }:
                            project.android.namespace = 'com.reactnativecommunity.asyncstorage'
                            break
                        case 'react-native-screens':
                            project.android.namespace = 'com.swmansion.rnscreens'
                            break
                        case 'react-native-vector-icons':
                            project.android.namespace = 'com.oblador.vectoricons'
                            break
                        case 'react-native-haptic-feedback':
                            project.android.namespace = 'com.mkuczera'
                            break
                    }
                }
            }
        }
    }
}

// rnmapbox_maps için özel konfigürasyon
subprojects { project ->
    if (project.name == "rnmapbox_maps") {
        project.afterEvaluate {
            def androidExt = project.extensions.findByName('android')
            if (androidExt != null) {
                androidExt.compileSdkVersion = 35
                androidExt.defaultConfig.targetSdkVersion 35
            }
            project.dependencies.add("implementation", "androidx.lifecycle:lifecycle-runtime:2.6.2")
            project.dependencies.add("implementation", "androidx.lifecycle:lifecycle-runtime-ktx:2.6.2")
            project.dependencies.add("api", "androidx.lifecycle:lifecycle-runtime:2.6.2")
            project.dependencies.add("api", "androidx.lifecycle:lifecycle-runtime-ktx:2.6.2")
        }
    }
}

