{"name": "@fishivo/config", "version": "1.0.0", "description": "Shared configuration for Fishivo", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx", "test": "jest"}, "keywords": ["config", "configuration", "constants"], "author": "Fishivo Team", "license": "MIT", "dependencies": {"@fishivo/shared": "workspace:*"}, "devDependencies": {"@types/jest": "^29.0.0", "@types/node": "^20.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "tsup": "^8.0.0", "typescript": "^5.0.0"}}