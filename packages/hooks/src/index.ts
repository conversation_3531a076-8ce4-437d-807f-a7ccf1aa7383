// Authentication hooks
export { useAuth } from './hooks/useAuth';
export { useProfile } from './hooks/useProfile';

// Data hooks
export { useCatches } from './hooks/useCatches';
export { useUsers } from './hooks/useUsers';
export { useFollow } from './hooks/useFollow';
export { useLikes } from './hooks/useLikes';

// Utility hooks
export { useUnits } from './hooks/useUnits';
export { useLocation } from './hooks/useLocation';
export { useWeather } from './hooks/useWeather';
export { useImagePicker } from './hooks/useImagePicker';

// Form hooks
export { useForm } from './hooks/useForm';
export { useValidation } from './hooks/useValidation';

// Storage hooks
export { useStorage } from './hooks/useStorage';

// Navigation hooks
export { useNavigation } from './hooks/useNavigation';
export { useRoute } from './hooks/useRoute';

// Contexts
export { AuthProvider, useAuth as useAuthContext } from './contexts/AuthContext';
export { FollowProvider, useFollow as useFollowContext } from './contexts/FollowContext';

// Context exports
export * from './contexts/AuthContext';
export * from './contexts/UnitsContext';
export * from './contexts/LocationContext';

// API hooks
export { useApi } from './useApi';
export { useSupabase } from './useSupabase';