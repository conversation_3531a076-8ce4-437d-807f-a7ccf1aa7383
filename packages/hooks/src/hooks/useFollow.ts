import { useState, useCallback } from 'react';
import { apiService } from '@fishivo/services';

interface FollowState {
  isFollowing: boolean;
  isPending: boolean;
  followersCount: number;
  followingCount: number;
  isLoading: boolean;
  error: string | null;
}

export const useFollow = (userId?: string) => {
  const [followState, setFollowState] = useState<FollowState>({
    isFollowing: false,
    isPending: false,
    followersCount: 0,
    followingCount: 0,
    isLoading: false,
    error: null,
  });

  const checkFollowStatus = useCallback(async (targetUserId: string) => {
    try {
      setFollowState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const status = await apiService.getFollowStatus(targetUserId);
      
      setFollowState(prev => ({
        ...prev,
        isFollowing: status.isFollowing,
        followersCount: status.followersCount,
        followingCount: status.followingCount,
        isLoading: false,
      }));
      
      return status;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Takip durumu kontrol edilirken hata oluştu';
      setFollowState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const followUser = useCallback(async (targetUserId: string) => {
    if (followState.isPending) return;
    
    try {
      setFollowState(prev => ({ 
        ...prev, 
        isPending: true, 
        error: null 
      }));
      
      await apiService.followUser(targetUserId);
      
      setFollowState(prev => ({
        ...prev,
        isFollowing: true,
        followersCount: prev.followersCount + 1,
        isPending: false,
      }));
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Takip edilirken hata oluştu';
      setFollowState(prev => ({
        ...prev,
        isPending: false,
        error: message,
      }));
      throw error;
    }
  }, [followState.isPending]);

  const unfollowUser = useCallback(async (targetUserId: string) => {
    if (followState.isPending) return;
    
    try {
      setFollowState(prev => ({ 
        ...prev, 
        isPending: true, 
        error: null 
      }));
      
      await apiService.unfollowUser(targetUserId);
      
      setFollowState(prev => ({
        ...prev,
        isFollowing: false,
        followersCount: Math.max(prev.followersCount - 1, 0),
        isPending: false,
      }));
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Takip bırakılırken hata oluştu';
      setFollowState(prev => ({
        ...prev,
        isPending: false,
        error: message,
      }));
      throw error;
    }
  }, [followState.isPending]);

  const toggleFollow = useCallback(async (targetUserId: string) => {
    if (followState.isFollowing) {
      return await unfollowUser(targetUserId);
    } else {
      return await followUser(targetUserId);
    }
  }, [followState.isFollowing, followUser, unfollowUser]);

  const getFollowers = useCallback(async (targetUserId: string) => {
    try {
      setFollowState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const followers = await apiService.getUserFollowers(targetUserId);
      
      setFollowState(prev => ({ ...prev, isLoading: false }));
      
      return followers;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Takipçiler yüklenirken hata oluştu';
      setFollowState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const getFollowing = useCallback(async (targetUserId: string) => {
    try {
      setFollowState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const following = await apiService.getUserFollowing(targetUserId);
      
      setFollowState(prev => ({ ...prev, isLoading: false }));
      
      return following;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Takip edilenler yüklenirken hata oluştu';
      setFollowState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const clearError = useCallback(() => {
    setFollowState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    isFollowing: followState.isFollowing,
    isPending: followState.isPending,
    followersCount: followState.followersCount,
    followingCount: followState.followingCount,
    isLoading: followState.isLoading,
    error: followState.error,
    checkFollowStatus,
    followUser,
    unfollowUser,
    toggleFollow,
    getFollowers,
    getFollowing,
    clearError,
  };
};
