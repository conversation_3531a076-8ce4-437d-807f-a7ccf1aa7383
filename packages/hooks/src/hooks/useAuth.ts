import { useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiService, supabase } from '@fishivo/services';

interface User {
  id: string;
  email: string;
  fullName?: string;
  avatar?: string;
  username?: string;
  bio?: string;
  location?: string;
  [key: string]: any;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  authError: string | null;
}

const AUTH_STORAGE_KEY = '@fishivo_auth_user';

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    authError: null,
  });

  useEffect(() => {
    loadStoredAuth();
    setupAuthListener();
  }, []);

  const loadStoredAuth = async () => {
    try {
      const storedUser = await AsyncStorage.getItem(AUTH_STORAGE_KEY);
      if (storedUser) {
        const user = JSON.parse(storedUser);
        setAuthState(prev => ({
          ...prev,
          user,
          isAuthenticated: true,
          isLoading: false,
        }));
      } else {
        setAuthState(prev => ({ ...prev, isLoading: false }));
      }
    } catch (error) {
      console.error('Auth yüklenirken hata:', error);
      setAuthState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const setupAuthListener = () => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          const user: User = {
            id: session.user.id,
            email: session.user.email || '',
            fullName: session.user.user_metadata?.full_name,
            avatar: session.user.user_metadata?.avatar_url,
            username: session.user.user_metadata?.username,
          };
          
          await AsyncStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(user));
          setAuthState(prev => ({
            ...prev,
            user,
            isAuthenticated: true,
            isLoading: false,
            authError: null,
          }));
        } else if (event === 'SIGNED_OUT') {
          await AsyncStorage.removeItem(AUTH_STORAGE_KEY);
          setAuthState({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            authError: null,
          });
        }
      }
    );

    return () => subscription.unsubscribe();
  };

  const login = useCallback(async (email: string, password: string) => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, authError: null }));
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      
      // Auth listener will handle the state update
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Giriş yapılırken hata oluştu';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        authError: message,
      }));
      throw error;
    }
  }, []);

  const register = useCallback(async (email: string, password: string, fullName: string, username?: string) => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, authError: null }));
      
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            username: username,
          },
        },
      });

      if (error) throw error;
      
      // Auth listener will handle the state update
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Kayıt olurken hata oluştu';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        authError: message,
      }));
      throw error;
    }
  }, []);

  const signInWithGoogle = useCallback(async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, authError: null }));
      
      const result = await apiService.signInWithGoogle();
      if (!result.success) {
        throw new Error('Google ile giriş başarısız');
      }
      
      // Auth listener will handle the state update
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Google ile giriş yapılırken hata oluştu';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        authError: message,
      }));
      throw error;
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));
      
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      // Auth listener will handle the state update
    } catch (error) {
      console.error('Çıkış yapılırken hata:', error);
      // Force logout even if there's an error
      await AsyncStorage.removeItem(AUTH_STORAGE_KEY);
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        authError: null,
      });
    }
  }, []);

  const updateProfile = useCallback(async (data: Partial<User>) => {
    try {
      if (!authState.user) throw new Error('Kullanıcı oturumu bulunamadı');
      
      setAuthState(prev => ({ ...prev, isLoading: true, authError: null }));
      
      const updatedUser = { ...authState.user, ...data };
      
      // Update in Supabase
      const { error } = await supabase.auth.updateUser({
        data: {
          full_name: updatedUser.fullName,
          username: updatedUser.username,
          avatar_url: updatedUser.avatar,
        },
      });

      if (error) throw error;
      
      // Update local storage and state
      await AsyncStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(updatedUser));
      setAuthState(prev => ({
        ...prev,
        user: updatedUser,
        isLoading: false,
      }));
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Profil güncellenirken hata oluştu';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        authError: message,
      }));
      throw error;
    }
  }, [authState.user]);

  const clearAuthError = useCallback(() => {
    setAuthState(prev => ({ ...prev, authError: null }));
  }, []);

  return {
    user: authState.user,
    isAuthenticated: authState.isAuthenticated,
    isLoading: authState.isLoading,
    authError: authState.authError,
    login,
    register,
    signInWithGoogle,
    logout,
    updateProfile,
    clearAuthError,
  };
};
