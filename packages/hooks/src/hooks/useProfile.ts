import { useState, useEffect, useCallback } from 'react';
import { apiService } from '@fishivo/services';

interface UserProfile {
  id: string;
  email: string;
  fullName?: string;
  username?: string;
  bio?: string;
  avatar?: string;
  location?: string;
  followersCount?: number;
  followingCount?: number;
  catchesCount?: number;
  isPrivate?: boolean;
  createdAt?: string;
  [key: string]: any;
}

interface ProfileState {
  profile: UserProfile | null;
  isLoading: boolean;
  error: string | null;
}

export const useProfile = (userId?: string) => {
  const [profileState, setProfileState] = useState<ProfileState>({
    profile: null,
    isLoading: false,
    error: null,
  });

  useEffect(() => {
    if (userId) {
      loadProfile(userId);
    }
  }, [userId]);

  const loadProfile = useCallback(async (targetUserId: string) => {
    try {
      setProfileState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const profile = await apiService.getUserProfile(targetUserId);
      
      setProfileState({
        profile,
        isLoading: false,
        error: null,
      });
      
      return profile;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Profil yüklenirken hata oluştu';
      setProfileState({
        profile: null,
        isLoading: false,
        error: message,
      });
      throw error;
    }
  }, []);

  const updateProfile = useCallback(async (data: Partial<UserProfile>) => {
    try {
      setProfileState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const updatedProfile = await apiService.updateUserProfile(data);
      
      setProfileState(prev => ({
        ...prev,
        profile: updatedProfile,
        isLoading: false,
      }));
      
      return updatedProfile;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Profil güncellenirken hata oluştu';
      setProfileState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const uploadAvatar = useCallback(async (imageUri: string) => {
    try {
      setProfileState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const avatarUrl = await apiService.uploadAvatar(imageUri);
      
      // Update profile with new avatar
      const updatedProfile = await updateProfile({ avatar: avatarUrl });
      
      return updatedProfile;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Avatar yüklenirken hata oluştu';
      setProfileState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, [updateProfile]);

  const deleteProfile = useCallback(async () => {
    try {
      setProfileState(prev => ({ ...prev, isLoading: true, error: null }));
      
      await apiService.deleteUserProfile();
      
      setProfileState({
        profile: null,
        isLoading: false,
        error: null,
      });
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Profil silinirken hata oluştu';
      setProfileState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const refreshProfile = useCallback(async () => {
    if (profileState.profile?.id) {
      return await loadProfile(profileState.profile.id);
    }
  }, [profileState.profile?.id, loadProfile]);

  const clearError = useCallback(() => {
    setProfileState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    profile: profileState.profile,
    isLoading: profileState.isLoading,
    error: profileState.error,
    loadProfile,
    updateProfile,
    uploadAvatar,
    deleteProfile,
    refreshProfile,
    clearError,
  };
};
