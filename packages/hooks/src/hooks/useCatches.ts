import { useState, useEffect, useCallback } from 'react';
import { apiService } from '@fishivo/services';

interface CatchRecord {
  id: string;
  userId: string;
  fishSpecies: string;
  weight?: number;
  length?: number;
  location: {
    latitude: number;
    longitude: number;
    name?: string;
  };
  photos: string[];
  description?: string;
  technique?: string;
  bait?: string;
  weather?: any;
  depth?: number;
  waterTemperature?: number;
  createdAt: string;
  updatedAt: string;
  likesCount?: number;
  commentsCount?: number;
  isLiked?: boolean;
  user?: {
    id: string;
    username: string;
    avatar?: string;
  };
  [key: string]: any;
}

interface CatchesState {
  catches: CatchRecord[];
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  page: number;
}

interface CreateCatchData {
  fishSpecies: string;
  weight?: number;
  length?: number;
  location: {
    latitude: number;
    longitude: number;
    name?: string;
  };
  photos: string[];
  description?: string;
  technique?: string;
  bait?: string;
  depth?: number;
  waterTemperature?: number;
}

export const useCatches = (userId?: string) => {
  const [catchesState, setCatchesState] = useState<CatchesState>({
    catches: [],
    isLoading: false,
    error: null,
    hasMore: true,
    page: 1,
  });

  useEffect(() => {
    loadCatches(true);
  }, [userId]);

  const loadCatches = useCallback(async (reset = false) => {
    try {
      const currentPage = reset ? 1 : catchesState.page;
      
      setCatchesState(prev => ({ 
        ...prev, 
        isLoading: true, 
        error: null,
        page: currentPage,
      }));
      
      const response = await apiService.getCatches({
        userId,
        page: currentPage,
        limit: 20,
      });
      
      setCatchesState(prev => ({
        ...prev,
        catches: reset ? response.catches : [...prev.catches, ...response.catches],
        isLoading: false,
        hasMore: response.hasMore,
        page: currentPage + 1,
      }));
      
      return response.catches;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Yakalamalar yüklenirken hata oluştu';
      setCatchesState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, [userId, catchesState.page]);

  const loadMoreCatches = useCallback(async () => {
    if (!catchesState.hasMore || catchesState.isLoading) return;
    return await loadCatches(false);
  }, [catchesState.hasMore, catchesState.isLoading, loadCatches]);

  const createCatch = useCallback(async (catchData: CreateCatchData) => {
    try {
      setCatchesState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const newCatch = await apiService.createCatch(catchData);
      
      setCatchesState(prev => ({
        ...prev,
        catches: [newCatch, ...prev.catches],
        isLoading: false,
      }));
      
      return newCatch;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Yakalama kaydedilirken hata oluştu';
      setCatchesState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const updateCatch = useCallback(async (catchId: string, updateData: Partial<CreateCatchData>) => {
    try {
      setCatchesState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const updatedCatch = await apiService.updateCatch(catchId, updateData);
      
      setCatchesState(prev => ({
        ...prev,
        catches: prev.catches.map(c => c.id === catchId ? updatedCatch : c),
        isLoading: false,
      }));
      
      return updatedCatch;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Yakalama güncellenirken hata oluştu';
      setCatchesState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const deleteCatch = useCallback(async (catchId: string) => {
    try {
      setCatchesState(prev => ({ ...prev, isLoading: true, error: null }));
      
      await apiService.deleteCatch(catchId);
      
      setCatchesState(prev => ({
        ...prev,
        catches: prev.catches.filter(c => c.id !== catchId),
        isLoading: false,
      }));
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Yakalama silinirken hata oluştu';
      setCatchesState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const likeCatch = useCallback(async (catchId: string) => {
    try {
      await apiService.likeCatch(catchId);
      
      setCatchesState(prev => ({
        ...prev,
        catches: prev.catches.map(c => 
          c.id === catchId 
            ? { 
                ...c, 
                isLiked: true, 
                likesCount: (c.likesCount || 0) + 1 
              }
            : c
        ),
      }));
      
    } catch (error) {
      console.error('Beğeni eklenirken hata:', error);
      throw error;
    }
  }, []);

  const unlikeCatch = useCallback(async (catchId: string) => {
    try {
      await apiService.unlikeCatch(catchId);
      
      setCatchesState(prev => ({
        ...prev,
        catches: prev.catches.map(c => 
          c.id === catchId 
            ? { 
                ...c, 
                isLiked: false, 
                likesCount: Math.max((c.likesCount || 0) - 1, 0) 
              }
            : c
        ),
      }));
      
    } catch (error) {
      console.error('Beğeni kaldırılırken hata:', error);
      throw error;
    }
  }, []);

  const refreshCatches = useCallback(async () => {
    return await loadCatches(true);
  }, [loadCatches]);

  const clearError = useCallback(() => {
    setCatchesState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    catches: catchesState.catches,
    isLoading: catchesState.isLoading,
    error: catchesState.error,
    hasMore: catchesState.hasMore,
    loadCatches,
    loadMoreCatches,
    createCatch,
    updateCatch,
    deleteCatch,
    likeCatch,
    unlikeCatch,
    refreshCatches,
    clearError,
  };
};
