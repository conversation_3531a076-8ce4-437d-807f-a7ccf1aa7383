import { useState, useCallback } from 'react';
import { apiService } from '@fishivo/services';

interface LikeState {
  isLiked: boolean;
  likesCount: number;
  isPending: boolean;
  error: string | null;
}

interface LikeData {
  id: string;
  userId: string;
  targetId: string;
  targetType: 'catch' | 'comment';
  createdAt: string;
  user?: {
    id: string;
    username: string;
    avatar?: string;
  };
}

export const useLikes = (targetId?: string, targetType: 'catch' | 'comment' = 'catch') => {
  const [likeState, setLikeState] = useState<LikeState>({
    isLiked: false,
    likesCount: 0,
    isPending: false,
    error: null,
  });

  const checkLikeStatus = useCallback(async (itemId: string, itemType: 'catch' | 'comment' = 'catch') => {
    try {
      const status = await apiService.getLikeStatus(itemId, itemType);
      
      setLikeState(prev => ({
        ...prev,
        isLiked: status.isLiked,
        likesCount: status.likesCount,
        error: null,
      }));
      
      return status;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Beğeni durumu kontrol edilirken hata oluştu';
      setLikeState(prev => ({
        ...prev,
        error: message,
      }));
      throw error;
    }
  }, []);

  const likeItem = useCallback(async (itemId: string, itemType: 'catch' | 'comment' = 'catch') => {
    if (likeState.isPending) return;
    
    try {
      setLikeState(prev => ({ 
        ...prev, 
        isPending: true, 
        error: null 
      }));
      
      if (itemType === 'catch') {
        await apiService.likeCatch(itemId);
      } else {
        await apiService.likeComment(itemId);
      }
      
      setLikeState(prev => ({
        ...prev,
        isLiked: true,
        likesCount: prev.likesCount + 1,
        isPending: false,
      }));
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Beğeni eklenirken hata oluştu';
      setLikeState(prev => ({
        ...prev,
        isPending: false,
        error: message,
      }));
      throw error;
    }
  }, [likeState.isPending]);

  const unlikeItem = useCallback(async (itemId: string, itemType: 'catch' | 'comment' = 'catch') => {
    if (likeState.isPending) return;
    
    try {
      setLikeState(prev => ({ 
        ...prev, 
        isPending: true, 
        error: null 
      }));
      
      if (itemType === 'catch') {
        await apiService.unlikeCatch(itemId);
      } else {
        await apiService.unlikeComment(itemId);
      }
      
      setLikeState(prev => ({
        ...prev,
        isLiked: false,
        likesCount: Math.max(prev.likesCount - 1, 0),
        isPending: false,
      }));
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Beğeni kaldırılırken hata oluştu';
      setLikeState(prev => ({
        ...prev,
        isPending: false,
        error: message,
      }));
      throw error;
    }
  }, [likeState.isPending]);

  const toggleLike = useCallback(async (itemId: string, itemType: 'catch' | 'comment' = 'catch') => {
    if (likeState.isLiked) {
      return await unlikeItem(itemId, itemType);
    } else {
      return await likeItem(itemId, itemType);
    }
  }, [likeState.isLiked, likeItem, unlikeItem]);

  const getLikes = useCallback(async (itemId: string, itemType: 'catch' | 'comment' = 'catch') => {
    try {
      setLikeState(prev => ({ ...prev, error: null }));
      
      const likes = await apiService.getLikes(itemId, itemType);
      
      return likes;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Beğeniler yüklenirken hata oluştu';
      setLikeState(prev => ({
        ...prev,
        error: message,
      }));
      throw error;
    }
  }, []);

  const getUserLikes = useCallback(async (userId: string) => {
    try {
      setLikeState(prev => ({ ...prev, error: null }));
      
      const userLikes = await apiService.getUserLikes(userId);
      
      return userLikes;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Kullanıcı beğenileri yüklenirken hata oluştu';
      setLikeState(prev => ({
        ...prev,
        error: message,
      }));
      throw error;
    }
  }, []);

  const clearError = useCallback(() => {
    setLikeState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    isLiked: likeState.isLiked,
    likesCount: likeState.likesCount,
    isPending: likeState.isPending,
    error: likeState.error,
    checkLikeStatus,
    likeItem,
    unlikeItem,
    toggleLike,
    getLikes,
    getUserLikes,
    clearError,
  };
};
