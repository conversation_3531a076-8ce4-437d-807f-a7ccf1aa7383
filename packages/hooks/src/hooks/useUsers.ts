import { useState, useEffect, useCallback } from 'react';
import { apiService } from '@fishivo/services';

interface User {
  id: string;
  email: string;
  fullName?: string;
  username?: string;
  bio?: string;
  avatar?: string;
  location?: string;
  followersCount?: number;
  followingCount?: number;
  catchesCount?: number;
  isPrivate?: boolean;
  isFollowing?: boolean;
  isFollowedBy?: boolean;
  createdAt?: string;
  [key: string]: any;
}

interface UsersState {
  users: User[];
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  page: number;
}

interface SearchFilters {
  query?: string;
  location?: string;
  sortBy?: 'newest' | 'popular' | 'catches';
}

export const useUsers = () => {
  const [usersState, setUsersState] = useState<UsersState>({
    users: [],
    isLoading: false,
    error: null,
    hasMore: true,
    page: 1,
  });

  const searchUsers = useCallback(async (filters: SearchFilters = {}, reset = true) => {
    try {
      const currentPage = reset ? 1 : usersState.page;
      
      setUsersState(prev => ({ 
        ...prev, 
        isLoading: true, 
        error: null,
        page: currentPage,
      }));
      
      const response = await apiService.searchUsers({
        ...filters,
        page: currentPage,
        limit: 20,
      });
      
      setUsersState(prev => ({
        ...prev,
        users: reset ? response.users : [...prev.users, ...response.users],
        isLoading: false,
        hasMore: response.hasMore,
        page: currentPage + 1,
      }));
      
      return response.users;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Kullanıcılar yüklenirken hata oluştu';
      setUsersState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, [usersState.page]);

  const loadMoreUsers = useCallback(async (filters: SearchFilters = {}) => {
    if (!usersState.hasMore || usersState.isLoading) return;
    return await searchUsers(filters, false);
  }, [usersState.hasMore, usersState.isLoading, searchUsers]);

  const getPopularUsers = useCallback(async (reset = true) => {
    return await searchUsers({ sortBy: 'popular' }, reset);
  }, [searchUsers]);

  const getNearbyUsers = useCallback(async (location: string, reset = true) => {
    return await searchUsers({ location, sortBy: 'newest' }, reset);
  }, [searchUsers]);

  const getFollowers = useCallback(async (userId: string) => {
    try {
      setUsersState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const followers = await apiService.getUserFollowers(userId);
      
      setUsersState(prev => ({
        ...prev,
        users: followers,
        isLoading: false,
        hasMore: false,
      }));
      
      return followers;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Takipçiler yüklenirken hata oluştu';
      setUsersState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const getFollowing = useCallback(async (userId: string) => {
    try {
      setUsersState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const following = await apiService.getUserFollowing(userId);
      
      setUsersState(prev => ({
        ...prev,
        users: following,
        isLoading: false,
        hasMore: false,
      }));
      
      return following;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Takip edilenler yüklenirken hata oluştu';
      setUsersState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const followUser = useCallback(async (userId: string) => {
    try {
      await apiService.followUser(userId);
      
      setUsersState(prev => ({
        ...prev,
        users: prev.users.map(user => 
          user.id === userId 
            ? { 
                ...user, 
                isFollowing: true,
                followersCount: (user.followersCount || 0) + 1
              }
            : user
        ),
      }));
      
    } catch (error) {
      console.error('Takip edilirken hata:', error);
      throw error;
    }
  }, []);

  const unfollowUser = useCallback(async (userId: string) => {
    try {
      await apiService.unfollowUser(userId);
      
      setUsersState(prev => ({
        ...prev,
        users: prev.users.map(user => 
          user.id === userId 
            ? { 
                ...user, 
                isFollowing: false,
                followersCount: Math.max((user.followersCount || 0) - 1, 0)
              }
            : user
        ),
      }));
      
    } catch (error) {
      console.error('Takip bırakılırken hata:', error);
      throw error;
    }
  }, []);

  const blockUser = useCallback(async (userId: string) => {
    try {
      await apiService.blockUser(userId);
      
      setUsersState(prev => ({
        ...prev,
        users: prev.users.filter(user => user.id !== userId),
      }));
      
    } catch (error) {
      console.error('Kullanıcı engellenirken hata:', error);
      throw error;
    }
  }, []);

  const reportUser = useCallback(async (userId: string, reason: string) => {
    try {
      await apiService.reportUser(userId, reason);
    } catch (error) {
      console.error('Kullanıcı şikayet edilirken hata:', error);
      throw error;
    }
  }, []);

  const clearUsers = useCallback(() => {
    setUsersState({
      users: [],
      isLoading: false,
      error: null,
      hasMore: true,
      page: 1,
    });
  }, []);

  const clearError = useCallback(() => {
    setUsersState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    users: usersState.users,
    isLoading: usersState.isLoading,
    error: usersState.error,
    hasMore: usersState.hasMore,
    searchUsers,
    loadMoreUsers,
    getPopularUsers,
    getNearbyUsers,
    getFollowers,
    getFollowing,
    followUser,
    unfollowUser,
    blockUser,
    reportUser,
    clearUsers,
    clearError,
  };
};
