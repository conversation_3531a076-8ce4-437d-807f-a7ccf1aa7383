import React from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { theme } from '../../../../../apps/mobile/src/theme';

interface WeatherLoadingStateProps {
  message?: string;
}

const WeatherLoadingState: React.FC<WeatherLoadingStateProps> = ({ message = 'Hava durumu yükleniyor...' }) => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color={theme.colors.primary} />
    <Text style={styles.loadingText}>{message}</Text>
  </View>
);

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: theme.spacing.md,
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
  },
});

export default WeatherLoadingState; 