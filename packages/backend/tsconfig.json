{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": false, "skipLibCheck": true, "noImplicitAny": false, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": false, "noImplicitThis": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": false, "resolveJsonModule": true, "declaration": false, "declarationMap": false, "sourceMap": false, "removeComments": true, "allowUnusedLabels": true, "allowUnreachableCode": true, "composite": true, "incremental": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "references": [{"path": "../shared"}, {"path": "../config"}, {"path": "../utils"}, {"path": "../services"}]}