import * as os from 'os';
import * as fs from 'fs';
import * as path from 'path';
import { ENV } from './index';

/**
 * Server Configuration
 * Profesyonel backend uygulamaları için sunucu yapılandırma modülü
 */
export const ServerConfig = {
  /**
   * Sunucu port yapılandırması
   * Öncelik: 
   * 1. <PERSON>tam değişkeni (PORT)
   * 2. Yapılandırma dosyası değeri
   * 3. <PERSON><PERSON><PERSON><PERSON><PERSON> (4000)
   */
  PORT: process.env.PORT || ENV.PORT || 4000,
  
  /**
   * Host yapılandırması
   * Öncelik:
   * 1. <PERSON><PERSON> (HOST)
   * 2. <PERSON>ars<PERSON><PERSON><PERSON> (0.0.0.0 - tüm network arayüzleri)
   */
  HOST: process.env.HOST || '0.0.0.0',
  
  /**
   * Sunucu varolan bir port üzerinde çalışamadığında yapılacak eylem
   * 'retry': alternatif port dene
   * 'exit': s<PERSON><PERSON><PERSON> sonlandır (varsayılan)
   */
  PORT_CONFLICT_ACTION: process.env.PORT_CONFLICT_ACTION || 'retry',
  
  /**
   * Port çakışması durumunda alternatif portların listesi
   */
  ALTERNATIVE_PORTS: [4001, 4002, 4003, 4004, 4005],
  
  /**
   * Port dublikasyonu kontrolü için PID dosyası
   * Bu dosya, aynı portu kullanan birden fazla sunucu sürecini önlemek için kullanılır
   */
  PID_FILE: path.join(os.tmpdir(), 'fishivo-backend.pid'),
  
  /**
   * API yollarındaki önek (prefix)
   * Örn: /api/users, /api/posts vb.
   */
  API_PREFIX: '/api',
  
  /**
   * Auth yollarındaki önek (prefix)
   * Örn: /auth/login, /auth/google vb.
   */
  AUTH_PREFIX: '/auth',
  
  /**
   * Sağlık kontrolü endpoint'i
   */
  HEALTH_ENDPOINT: '/health',
  
  /**
   * Sunucu başlatma stratejisi
   * 'graceful': Varolan bağlantılar tamamlanana kadar bekle
   * 'force': Hemen yeni bir sunucu başlat
   */
  STARTUP_STRATEGY: 'graceful',
  
  /**
   * PID dosyası oluştur
   * Bu fonksiyon, çalışan sunucu sürecinin PID'sini bir dosyaya kaydeder
   * Bu sayede aynı uygulamanın birden fazla instance'ının aynı portu kullanması önlenir
   */
  writePidFile() {
    try {
      fs.writeFileSync(this.PID_FILE, process.pid.toString());
      console.log(`📝 PID file written: ${this.PID_FILE} (PID: ${process.pid})`);
    } catch (error) {
      console.warn(`⚠️ Could not write PID file: ${error}`);
    }
  },
  
  /**
   * PID dosyasını kontrol et
   * Bu fonksiyon, aynı uygulamanın başka bir instance'ının çalışıp çalışmadığını kontrol eder
   */
  checkPidFile(): boolean {
    try {
      if (fs.existsSync(this.PID_FILE)) {
        const pid = fs.readFileSync(this.PID_FILE, 'utf-8');
        console.log(`🔍 Found existing PID file: ${pid}`);
        
        // PID'nin geçerli olup olmadığını kontrol et
        try {
          process.kill(parseInt(pid, 10), 0);
          console.warn(`⚠️ Process with PID ${pid} is still running`);
          return true; // Süreç hala çalışıyor
        } catch (e) {
          console.log(`🔄 Stale PID file found, process ${pid} is not running`);
          return false; // Süreç çalışmıyor, PID dosyası eski
        }
      }
    } catch (error) {
      console.warn(`⚠️ Error checking PID file: ${error}`);
    }
    return false;
  },
  
  /**
   * PID dosyasını temizle
   */
  cleanPidFile() {
    try {
      if (fs.existsSync(this.PID_FILE)) {
        fs.unlinkSync(this.PID_FILE);
        console.log(`🧹 PID file removed: ${this.PID_FILE}`);
      }
    } catch (error) {
      console.warn(`⚠️ Error removing PID file: ${error}`);
    }
  },
  
  /**
   * Port çakışması durumunda alternatif port bul
   */
  findAlternativePort(): number {
    for (const port of this.ALTERNATIVE_PORTS) {
      try {
        // Bu noktada gerçek bir port kontrolü yapmak için net modülü kullanılabilir
        // Basitlik için sadece alternatif portu dönüyoruz
        console.log(`🔄 Trying alternative port: ${port}`);
        return port;
      } catch (err) {
        console.log(`❌ Port ${port} is also in use`);
      }
    }
    console.error('❌ No available ports found in the alternative ports list');
    return 0; // Hiçbir port kullanılamıyor
  }
};

/**
 * Uygulama sonlandığında PID dosyasını temizle
 */
process.on('exit', () => {
  ServerConfig.cleanPidFile();
});

process.on('SIGINT', () => {
  console.log('👋 Gracefully shutting down from SIGINT (Ctrl+C)');
  ServerConfig.cleanPidFile();
  process.exit(0);
});

export default ServerConfig; 